module.exports = {
    extends: [
        "eslint:recommended",
        "plugin:@typescript-eslint/recommended",
        "plugin:import/recommended",
        "plugin:promise/recommended",
        "plugin:sonarjs/recommended-legacy"
    ],
    rules: {
        "@typescript-eslint/ban-ts-comment": "off",
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/no-unused-vars": "off",
        "@typescript-eslint/no-var-requires": "off",
        "@typescript-eslint/no-namespace": "off",
        "@typescript-eslint/no-loss-of-precision": "off",
        "@typescript-eslint/no-empty-function": "off",
        "@typescript-eslint/ban-types": "off",
        "@typescript-eslint/no-this-alias": "off",
        "prefer-rest-params": "off",
        "@typescript-eslint/no-inferrable-types": "off",
        "prefer-spread": "off",
        // "@typescript-eslint/consistent-type-imports": "error",
        // "@typescript-eslint/consistent-type-exports": "error",
        "semi": ["error", "always"],
        "semi-style": ["error", "last"],
        "semi-spacing": ["warn", { "before": false, "after": true }],
        "comma-dangle": "off",
        "no-extra-semi": ["warn"],
        "quotes": ["error", "double"],
        "no-multi-spaces": "error",
        "arrow-spacing": "error",
        "no-duplicate-imports": "error",
        "space-before-blocks": "off",
        "@typescript-eslint/space-before-blocks": "error",
        "@typescript-eslint/prefer-includes": "error",
        "prefer-promise-reject-errors": "error",
        "block-spacing": "error",
        "unicorn/filename-case": [
            "error",
            {
                "cases": {
                    "camelCase": true
                }
            }
        ],
        "unicorn/prefer-array-find": "error",
        "node/no-sync": "error",
        "node/no-callback-literal": "error",
        "unicorn/no-lonely-if": "error",
        "unicorn/prefer-ternary": "error",
        "unicorn/import-style": "error",
        "unicorn/prefer-set-has": "error",
        "unicorn/throw-new-error": "error",
        "unicorn/no-array-for-each": "error",
        "import/no-cycle": "error",
        "import/named": "off",
        "import/namespace": "off",
        "promise/no-return-wrap": ["error", { allowReject: true }],
        "sonarjs/no-duplicate-string": "off",
        "sonarjs/cognitive-complexity": "off",
        "no-async-promise-executor": "off"
    },
    ignorePatterns: [
        "lib/*",
        "lib/**/*",
        ".eslintrc.js",
        "eslint.config.js",
        "node_modules/*",
        "node_modules/**/*",
    ],
    parserOptions: {
        ecmaVersion: 2022,
        sourceType: "module",
        project: [
            require.resolve('./tsconfig.json')
        ],
    },
    plugins: [
        "@typescript-eslint",
        "unicorn",
        "node",
        "import",
        "promise",
        "sonarjs"
    ],
    env: {
        node: true,
        es2022: true,
    },
    parser: "@typescript-eslint/parser",
    settings: {
        "import/parsers": {
            "@typescript-eslint/parser": [".ts"]
        },
        "import/resolver": {
            "typescript": {
                "alwaysTryTypes": true,
                "project": "./",
            }
        }
    }
}
