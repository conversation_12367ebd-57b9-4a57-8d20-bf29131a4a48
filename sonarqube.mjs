import scanner from "sonarqube-scanner";
import { readFile } from "node:fs/promises";
import { setInterval } from "node:timers";

const SONAR_CONFIG = {
    serverUrl: process.env.SONAR_HOST_URL || "https://sonarqube-k8s.internal.skywindservices.com",
    token: process.env.SONAR_AUTH_TOKEN,
};

const get = async (url) => {
    const myHeaders = new Headers();
    myHeaders.append("Authorization", `Bearer ${SONAR_CONFIG.token}`);

    return fetch(url, { method: "GET", headers: myHeaders }).then(response => {
        if (response.status === 401) {
            console.log("Unauthorized");
            process.exit(0);
        }
        return response.json()
    }).catch(error => {
        console.log(error.message);
        process.exit(0);
    });
};
async function main() {
    const options = { serverUrl: SONAR_CONFIG.serverUrl };
    if (!SONAR_CONFIG.token) {
        console.log("Token not found");
        return;
    }
    options.token = SONAR_CONFIG.token;
    scanner(options, async (errors) => {
        if (errors) return process.exit(1);
        const file = await readFile(".scannerwork/report-task.txt", { encoding: "utf8" });
        const sonarCeTaskURL = file
            .split("\n")
            .find(str => str.indexOf("ceTaskUrl") !== -1)
            .replace("ceTaskUrl=", "");

        let intervalId = setInterval(async () => {
            const { task } = await get(sonarCeTaskURL);
            if (!task || task.status === "IN_PROGRESS") {
                return;
            }
            clearInterval(intervalId);

            const analysisId = task.analysisId;
            const reportUrl = `${SONAR_CONFIG.serverUrl}/api/qualitygates/project_status?analysisId=${analysisId}`;
            const { projectStatus } = await get(reportUrl);
            if (!projectStatus) {
                console.log("projectStatus not found");
                return ;
            }
            if (projectStatus && projectStatus.status === "ERROR") {
                console.log(`Sonar analysis is fail. Please check results here: ${reportUrl}`, projectStatus);
            } else {
                console.log(JSON.stringify(projectStatus));
            }
            process.exit(0);
        }, 1000)
    });
}

void main();
