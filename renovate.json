{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended"], "bumpVersion": "patch", "automerge": false, "internalChecksFilter": "strict", "configMigration": true, "osvVulnerabilityAlerts": true, "packageRules": [{"groupName": "Dev libraries", "matchDepTypes": ["devDependencies"], "rangeStrategy": "bump", "schedule": ["before 04:00am on tuesday"]}, {"enabled": false, "matchDatasources": ["docker"], "matchUpdateTypes": ["major"]}, {"enabled": false, "matchPackageNames": ["chai", "prom-client"], "matchUpdateTypes": ["major"]}, {"enabled": false, "matchPackageNames": ["prom-client"], "matchUpdateTypes": ["minor"]}], "schedule": ["before 5am on Friday"]}