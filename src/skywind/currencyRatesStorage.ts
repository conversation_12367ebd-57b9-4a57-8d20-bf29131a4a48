import { ExchangeRate } from "./types";
import { RedisPool } from "./redisTypes";

/**
 * Redis client for currency exchange service.
 */
export class RedisStorage {

    constructor(private pool: RedisPool, private dbKeyPrefix: string) {}

    public async get(id: string): Promise<ExchangeRate> {
        const key = this.getDbKey(id);
        const redisClient = await this.pool.get();
        try {
            const data = await redisClient.get(key);
            return data ? JSON.parse(data) : undefined;
        } finally {
            this.pool.release(redisClient);
        }
    }

    public async set(id: string, rates: ExchangeRate, ttl?: number): Promise<void> {
        const key = this.getDbKey(id);
        const data = JSON.stringify(rates);
        const redisClient = await this.pool.get();
        try {
            ttl ? await redisClient.setex(key, ttl, data) : await redisClient.set(key, data);
        } finally {
            this.pool.release(redisClient);
        }
    }

    public async lockUpdate(ttl: number): Promise<boolean> {
        const key = `${this.dbKeyPrefix}:lock`;
        const redisClient = await this.pool.get();
        try {
            const result = await redisClient.set(key, Date.now(), "EX", ttl, "NX");
            return result !== null;
        } finally {
            this.pool.release(redisClient);
        }
    }

    public async unlockUpdate(): Promise<number> {
        const key = `${this.dbKeyPrefix}:lock`;
        const redisClient = await this.pool.get();
        try {
            return await redisClient.del(key);
        } finally {
            this.pool.release(redisClient);
        }
    }

    private getDbKey(key: string): string {
        return `${this.dbKeyPrefix}:${key}`;
    }
}
