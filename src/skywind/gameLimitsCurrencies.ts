import { GameLimitsCurrency } from "./types";
import { generateToken } from "./utils";
import * as superagent from "superagent";
import config from "./config";

/**
 * Get full list of game limits currencies instances
 */
export async function getGameLimitsCurrencies(): Promise<GameLimitsCurrency[]> {
    const token = await generateToken(config.sws.internalServerToken);
    const url = `${config.sws.baseUrl}/v1/game-limits-currencies`;
    return new Promise<GameLimitsCurrency[]>(async (resolve, reject) => {
        return superagent.get(url).query({ token })
            .then(response => resolve(response.body))
            .catch(error => reject(error));
    });
}

/**
 * Get game limits currency found by currency code and version
 * @param currency - Currency code
 * @param version - Version of game limits currency parameters set
 */
export async function getGameLimitsCurrency(currency: string, version: number): Promise<GameLimitsCurrency> {
    const token = await generateToken(config.sws.internalServerToken);
    const url = `${config.sws.baseUrl}/v1/game-limits-currencies/${currency}/${version}`;
    return new Promise<GameLimitsCurrency>(async (resolve, reject) => {
        return superagent.get(url).query({ token })
            .then(response => resolve(response.body))
            .catch(error => reject(error));
    });
}

/**
 * Update game limits currency instance found by currency code and version
 * @param currency - Currency code
 * @param version - Version of game limits currency parameters set
 * @param gameLimitsCurrency - body for update game limits currency instance (toEURMultiplier and copyLimitsFrom)
 */
export async function updateGameLimitsCurrency(currency: string,
                                               version: number,
                                               gameLimitsCurrency: GameLimitsCurrency): Promise<GameLimitsCurrency> {
    const token = await generateToken(config.sws.internalServerToken);
    const url = `${config.sws.baseUrl}/v1/game-limits-currencies/${currency}/${version}`;
    return new Promise<GameLimitsCurrency>(async (resolve, reject) => {
        return superagent.patch(url)
            .query({
                token,
                toEURMultiplier: gameLimitsCurrency.toEURMultiplier,
                copyLimitsFrom: gameLimitsCurrency.copyLimitsFrom
            })
            .then(response => {
                if (response.statusCode !== 200) {
                    return reject(new Error(`${response.body?.message}, status=${response.statusCode}`));
                }
                return resolve(response.body);
            })
            .catch(error => reject(error));
    });
}

/**
 * Create new game limits currency instance
 * @param gameLimitsCurrency - body for update game limits currency instance (currency, version, toEURMultiplier
 * and copyLimitsFrom)
 */
export async function createGameLimitsCurrency(gameLimitsCurrency: GameLimitsCurrency): Promise<GameLimitsCurrency> {
    const token = await generateToken(config.sws.internalServerToken);
    const url = `${config.sws.baseUrl}/v1/game-limits-currencies`;
    return new Promise<GameLimitsCurrency>(async (resolve, reject) => {
        return superagent.post(url)
            .query({
                token,
                currency: gameLimitsCurrency.currency,
                version: gameLimitsCurrency.version,
                toEURMultiplier: gameLimitsCurrency.toEURMultiplier,
                copyLimitsFrom: gameLimitsCurrency.copyLimitsFrom,
            })
            .then(response => {
                if (response.statusCode !== 201) {
                    return reject(new Error(`${response.body?.message}, status=${response.statusCode}`));
                }
                return resolve(response.body);
            })
            .catch(error => reject(error));
    });
}

/**
 * Remove game limits currency instance found by currency code and version
 * @param currency - Currency code
 * @param version - Version of game limits currency parameters set
 */
export async function deleteGameLimitsCurrency(currency: string, version: number): Promise<void> {
    const token = await generateToken(config.sws.internalServerToken);
    const url = `${config.sws.baseUrl}/v1/game-limits-currencies/${currency}/${version}`;
    return new Promise<void>(async (resolve, reject) => {
        return superagent.delete(url)
            .query({ token })
            .then(response => resolve(response.body))
            .catch(error => reject(error));
    });
}
