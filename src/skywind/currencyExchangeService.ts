// this should be the first line
import { logging, measures } from "@skywind-group/sw-utils";
// this should be the second line
measures.measureProvider.baseInstrument();
import { Currencies } from "./currencies";
import {
    type CurrencyExchange,
    type CurrencyProvider,
    CurrencyRatesOutdatedError,
    CurrencyRatesUnavailableError,
    type CustomExchangeRate,
    type ExchangeRate,
    type ExchangeRateType,
    VirtualCurrencyRateError,
} from "./types";
import { type CurrencyExchangeOptions, sanitizeSensitiveData } from "./config";
import { getEndTime, getNextDay, getTimestamp } from "./utils";
import { scheduleJob } from "node-schedule";
import { RedisStorage } from "./currencyRatesStorage";
import { RedisPool } from "./redisTypes";

const log = logging.logger("currency-service");

import Timeout = NodeJS.Timeout;
import measureProvider = measures.measureProvider;

export class CurrencyExchangeService implements CurrencyExchange {

    private static readonly CURRENT_RATES = "current";

    private config: CurrencyExchangeOptions;
    private db: RedisStorage;
    private provider: CurrencyProvider;
    private type: ExchangeRateType;
    private updateTimeoutID: Timeout;
    private updateJob;

    // Store actual currencies rates list
    private currentRates: ExchangeRate;
    // Store upcoming currency rates list
    private upcomingRates: ExchangeRate;

    /**
     * Must be called once before first usage
     * @param pool - redis storage
     * @param provider - currency rates provider (oanda, oxr, sws)
     * @param options - configuration options
     * @param type - bid or ask
     */
    public async init(pool: RedisPool,
                      provider: CurrencyProvider,
                      options: CurrencyExchangeOptions,
                      type: ExchangeRateType): Promise<void> {
        return measureProvider.runInTransaction("CurrencyExchangeService.init", async () => {
            this.config = options;
            log.info("Init currency exchange service with config:", sanitizeSensitiveData(this.config));
            if (!this.config.baseCurrencies.includes(this.config.defaultBaseCurrency)) {
                log.warn("Default base currency is not in the list of base currencies. " +
                    "First available currency will be used to make conversion between non-base currencies");
            }

            this.db = new RedisStorage(pool, `${options.dbKeyPrefix}:${options.provider}`);
            this.provider = provider;
            this.type = type;

            // update current and upcoming rates
            await this.initCurrentRates();
            await this.initUpcomingRates();

            // schedule future updates
            this.updateJob = scheduleJob(this.config.updateSchedule, () => {
                return measureProvider.runInTransaction("CurrencyExchangeService.scheduledJob", () => {
                    const today = new Date();
                    return this.updateWithRetry(getNextDay(today), getEndTime(today));
                });
            });
        });
    }

    /**
     * For test purposes
     */
    public clean() {
        clearTimeout(this.updateTimeoutID);
        this.updateTimeoutID = null;
        if (this.updateJob) {
            this.updateJob.cancel();
        }
    }

    public exchange(amount: number,
                    baseCurrency: string,
                    targetCurrency: string,
                    currencyRate?: CustomExchangeRate): number {
        const exchanged = this.moneyExchange(amount, baseCurrency, targetCurrency, currencyRate);
        return Currencies.format(targetCurrency, exchanged);
    }

    public getExchangeRate(baseCurrency: string,
                           targetCurrency: string,
                           currencyRate?: CustomExchangeRate): number {
        return this.moneyExchange(1, baseCurrency, targetCurrency, currencyRate);
    }

    public exchangeWithRate(amount: number, exchangeRate: number, targetCurrency: string): number {
        const exchanged = amount * exchangeRate;
        return Currencies.format(targetCurrency, exchanged);
    }

    public getExchangeRatesList(): ExchangeRate {
        const currentRates = this.getCurrentRates();
        const result: ExchangeRate = { ...currentRates };

        // Add artificial currencies
        for (const base of Object.keys(currentRates.rates)) {
            const rates = { ...currentRates.rates[base] };
            for (const { code, originCurrency } of Currencies.artificialValues()) {
                if (originCurrency) {
                    rates[code] = +rates[originCurrency.currency] / originCurrency.multiplier;
                }
            }
            result.rates[base] = rates;
        }

        return result;
    }

    private moneyExchange(amount: number,
                          baseCurrency: string,
                          targetCurrency: string,
                          currencyRate?: CustomExchangeRate): number {
        const currentRates = this.getCurrentRates().rates;
        const bCurrency = Currencies.get(baseCurrency);
        const tCurrency = Currencies.get(targetCurrency);

        if (bCurrency === tCurrency) {
            return amount;
        }

        if (currencyRate) {
            let exchangeRate: number;
            if (currencyRate[baseCurrency] && currencyRate[baseCurrency][targetCurrency]) {
                exchangeRate = currencyRate[baseCurrency][targetCurrency];
            } else if (currencyRate[targetCurrency] && currencyRate[targetCurrency][baseCurrency]) {
                exchangeRate = 1 / currencyRate[targetCurrency][baseCurrency];
            }

            if (exchangeRate) {
                return amount * exchangeRate;
            }
        }

        if (bCurrency.isVirtual || tCurrency.isVirtual) {
            throw new VirtualCurrencyRateError();
        }

        const baseArtificial = bCurrency.originCurrency;
        if (baseArtificial) {
            amount = amount * baseArtificial.multiplier;
            baseCurrency = baseArtificial.currency;
        }
        const targetArtificial = tCurrency.originCurrency;
        if (targetArtificial) {
            targetCurrency = targetArtificial.currency;
        }

        let exchangeRate: number;
        if (currentRates[baseCurrency] && currentRates[baseCurrency][targetCurrency]) {
            exchangeRate = currentRates[baseCurrency][targetCurrency];
        } else if (currentRates[targetCurrency] && currentRates[targetCurrency][baseCurrency]) {
            exchangeRate = 1 / currentRates[targetCurrency][baseCurrency];
        } else {
            // there is no direct currency rate between base and target
            // we have to make conversion through third currency
            let conversionCurrency;
            if (currentRates[this.config.defaultBaseCurrency] &&
                currentRates[this.config.defaultBaseCurrency][baseCurrency] &&
                currentRates[this.config.defaultBaseCurrency][targetCurrency]) {
                conversionCurrency = this.config.defaultBaseCurrency;
            } else {
                // default conversion currency doesn't have rates, search for any other available currency
                for (const currency of Object.keys(currentRates)) {
                    if (currentRates[currency][baseCurrency] && currentRates[currency][targetCurrency]) {
                        conversionCurrency = currency;
                        break;
                    }
                }
            }

            if (conversionCurrency) {
                const rateFrom = currentRates[conversionCurrency][baseCurrency];
                const rateTo = currentRates[conversionCurrency][targetCurrency];
                exchangeRate = rateTo * (1 / rateFrom);
            }
        }

        if (!exchangeRate) {
            throw new CurrencyRatesUnavailableError();
        }

        let exchanged = amount * exchangeRate;

        if (targetArtificial) {
            exchanged = exchanged / targetArtificial.multiplier;
        }

        return exchanged;
    }

    private async updateWithRetry(ratesDate: Date, expire: number): Promise<void> {
        if (Date.now() > expire) {
            log.error(new CurrencyRatesUnavailableError(), "Currency provider is not available. Give up to update");
            return;
        }
        try {
            await this.update(ratesDate);
        } catch (err) {
            log.warn(new CurrencyRatesUnavailableError(err),
                "Currency provider is not available. Retry to update in %d", this.config.failedUpdateTimeout);
            this.updateTimeoutID = setTimeout(() => {
                return this.updateWithRetry(ratesDate, expire);
            }, this.config.failedUpdateTimeout);
        }
    }

    private async update(date: Date): Promise<ExchangeRate> {
        log.debug("Update currency rates for", date);
        const rates = await new Promise<ExchangeRate>((resolve, reject) => {
            this.doUpdate(date, resolve, reject);
        });
        log.debug("Currency rates updated for", date);
        return rates;
    }

    /**
     * Update currencies for the specified date.
     */
    private async doUpdate(date: Date, resolve, reject) {
        try {
            const ratesId = `${getTimestamp(date)}:${this.type}`;
            let rates: ExchangeRate = await this.db.get(ratesId);

            if (rates) {
                this.setMoney(rates);
                return resolve(rates);
            }

            const locked = await this.db.lockUpdate(this.config.lockTtl);
            if (!locked) {
                log.debug("Concurrent currency rates update. Wait for unlock and retry");
                this.updateTimeoutID = setTimeout(() => {
                    return this.doUpdate(date, resolve, reject);
                }, this.config.lockRetryTimeout);
                return;
            }

            try {
                log.info("Get currency rates for %s from %s for %s currencies",
                    date.toISOString(), this.config.provider, this.config.baseCurrencies);
                rates = await this.provider.getRates(date, this.config.baseCurrencies, this.type);
                this.setMoney(rates);
                await this.db.set(ratesId, rates, this.config.dbRatesTtl);
                log.info("Currency rates stored for %s", date.toISOString());
            } finally {
                await this.db.unlockUpdate();
            }

            resolve(rates);
        } catch (err) {
            reject(err);
        }
    }

    /**
     * Set rates for currency exchange module
     * @param rates - oxr response structure
     */
    private setMoney(rates: ExchangeRate) {
        const now = Date.now();
        if (now >= rates.startTime && now <= rates.endTime) {
            log.info(`Update current exchange rates: provider ts: ${rates.ts}`);
            this.currentRates = rates;
        } else if (now < rates.startTime) {
            log.info(`Update upcoming exchange rates: provider ts: ${rates.ts}`);
            this.upcomingRates = rates;
            setTimeout(this.applyUpcomingRates.bind(this), this.upcomingRates.startTime - now);
        }
    }

    private getCurrentRates(): ExchangeRate {
        if (!this.currentRates) {
            throw new CurrencyRatesUnavailableError();
        }
        const now = Date.now();
        if (now > this.currentRates.endTime) {
            if (this.upcomingRates) {
                this.applyUpcomingRates();
            } else {
                log.error(new CurrencyRatesOutdatedError());
            }
        }
        return this.currentRates;
    }

    private async initCurrentRates(): Promise<void> {
        const currentRatesId = `${CurrencyExchangeService.CURRENT_RATES}:${this.type}`;
        const dbRates: ExchangeRate = await this.db.get(currentRatesId);
        const today = new Date();
        if (dbRates && today.getTime() >= dbRates.startTime && today.getTime() <= dbRates.endTime) {
            // rates are up-to-date
            return this.setMoney(dbRates);
        }

        let currentRates;
        try {
            currentRates = await this.update(today);
        } catch (err) {
            log.error(err, "Failed to get current rates");

            if (dbRates) {
                log.warn("Use outdated rates: provider ts=%s, startTime=%d, endTime=%d",
                    dbRates.ts, dbRates.startTime, dbRates.endTime);
                this.currentRates = dbRates;
            } else {
                log.error(err, "Failed to get current rates");
                return Promise.reject(new CurrencyRatesUnavailableError());
            }

            return;
        }

        if (currentRates) {
            await this.db.set(currentRatesId, currentRates);
            this.setMoney(currentRates);
        }
    }

    private async initUpcomingRates() {
        const today = new Date();
        await this.updateWithRetry(getNextDay(), getEndTime(today));
    }

    private applyUpcomingRates() {
        if (!this.upcomingRates) {
            log.info("New currency rates was already applied");
            return;
        }
        log.info("Apply new currency rates: provider ts=%s, startTime=%d, endTime=%d",
            this.upcomingRates.ts, this.upcomingRates.startTime, this.upcomingRates.endTime);
        this.currentRates = this.upcomingRates;
        this.upcomingRates = undefined;

        (async () => {
            try {
                const currentRatesId = `${CurrencyExchangeService.CURRENT_RATES}:${this.type}`;
                const dbRates: ExchangeRate = await this.db.get(currentRatesId);
                const now = Date.now();
                if (dbRates && now >= dbRates.startTime && now <= dbRates.endTime) {
                    // already updated
                    return;
                }
                if (dbRates.provider !== "default") {
                    await this.db.set(currentRatesId, this.currentRates);
                }
            } catch (err) {
                log.error(err, "Failed to update current rates in db: ts=%s, startTime=%d, endTime=%d",
                    this.currentRates.ts, this.currentRates.startTime, this.currentRates.endTime);
            }
        })();
    }
}
