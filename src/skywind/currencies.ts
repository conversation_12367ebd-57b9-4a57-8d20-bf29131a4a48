import { CurrencyNotFoundError } from "./types";
import currenciesResource from "./resources/currencies.json";
import artificialCurrenciesResource from "./resources/artificial_currencies.json";

const EPSILON = 10 ** -12;

export interface OriginCurrency {
    currency: string;
    multiplier: number;
}

interface Iso {
    code: string;
    number: string;
    /** Number of decimal digits after comma (example: 3 == ".000") */
    minorUnits: number;
}

interface ClientMoneyFormat {
    code?: string;
    /** Enables short mode postfixes K:10^3, M:10^6, B:10^9 (example: $1500 = $1.5K ) */
    shortMode?: boolean;
}

interface CurrencyInfo {
    readonly name: string;
    readonly iso: Iso;
    readonly type?: string;
    readonly toEURMultiplier?: number;
    readonly copyLimitsFrom?: string;
    readonly provider?: string;
    /** Used to update currencyMultiplier on game-server for "Bank It" feature */
    readonly clientMinorUnits?: number;
    readonly clientMoneyFormat?: ClientMoneyFormat;
    readonly disableGGR?: boolean;
    readonly funBonus?: boolean;
}

export class Currency {
    /** Virtual currencies for promotion rewards. */
    public readonly isVirtual: boolean;
    public readonly isSocial: boolean;
    public readonly multiplier: number;
    public readonly exponent: number;
    public readonly clientMultiplier: number;
    public readonly funBonus: boolean;

    constructor(
        public readonly code: string,
        public readonly name: string,
        public readonly iso: Iso,
        type?: "virtual" | "social",
        public readonly toEURMultiplier?: number,
        public readonly copyLimitsFrom?: string,
        public readonly provider?: string,
        public readonly originCurrency?: OriginCurrency,
        clientMinorUnits?: number,
        public readonly clientMoneyFormat?: ClientMoneyFormat,
        public readonly disableGGR?: boolean,
        funBonus?: boolean
    ) {
        this.isVirtual = type === "virtual";
        this.isSocial = type === "social";
        this.multiplier = 10 ** iso.minorUnits;
        this.exponent = String(this.multiplier).length - 1;
        this.clientMultiplier = clientMinorUnits !== undefined ? (10 ** clientMinorUnits) : this.multiplier;
        this.funBonus = funBonus || false;
    }

    public toMinorUnits(value: number): number {
        return Math.round(value * this.multiplier);
    }

    public toMajorUnits(value: number): number {
        if (!Number.isInteger(value)) {
            throw new Error("Invalid currency value: " + value);
        }
        return value / this.multiplier; // assuming the result mustn't have an accumulated floating point error
    }

    public format(value: number): number {
        const precision = 10 ** this.exponent;
        return Math.sign(value) * Math.floor(Math.abs(value) * precision + EPSILON) / precision;
    }

    public toFixedByExponent(value: number): number {
        return Number((value).toFixed(this.exponent));
    }
}

const artificialCurrencies: Record<string, OriginCurrency> = Object.fromEntries(Object.entries(artificialCurrenciesResource).map(([code, item]) => [code, {
    currency: item.originCurrency,
    multiplier: item.multiplier
}]));

const currencies = Object.fromEntries(Object.entries<CurrencyInfo>(currenciesResource).map(([code, item]) => [
    code,
    new Currency(
        code,
        item.name,
        item.iso,
        item.type === "virtual" ? "virtual" : item.type === "social" ? "social" : undefined,
        item.toEURMultiplier,
        item.copyLimitsFrom,
        item.provider,
        artificialCurrencies[code],
        item.clientMinorUnits,
        item.clientMoneyFormat,
        item.disableGGR,
        item.funBonus
    )
]));

const currencyCodes = Object.keys(currencies);
const currencyValues = Object.values(currencies);
const artificialValues = currencyValues
    .filter(({ originCurrency, isVirtual }) => Boolean(originCurrency) && !isVirtual);

export const Currencies = {

    exists(currencyCode: string): boolean {
        return currencies[currencyCode] !== undefined;
    },

    keys(): string[] {
        return currencyCodes;
    },

    values(): Currency[] {
        return currencyValues;
    },

    artificialValues(): Currency[] {
        return artificialValues;
    },

    value(currencyCode: string): Currency | undefined {
        return currencies[currencyCode];
    },

    get(currencyCode: string): Currency {
        this.verifyExists(currencyCode);
        return currencies[currencyCode];
    },

    verifyExists(currencyCode: string) {
        if (!currencies[currencyCode]) {
            throw new CurrencyNotFoundError(currencyCode);
        }
    },

    format(currencyCode: string, value: number): number {
        return this.get(currencyCode).format(value);
    },
};
