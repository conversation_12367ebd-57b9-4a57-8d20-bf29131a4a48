import { logging } from "@skywind-group/sw-utils";
import { CurrencyProvider, ExchangeRate, ExchangeRateProvider, ExchangeRateType } from "../types";
import { generateToken, getEndTime, getStartTime, getTimestamp } from "../utils";
import * as superagent from "superagent";

const log = logging.logger("sws-currency-provider");

export interface SWSExchangeRates {
    date: string;
    source: {
        date: string;
        provider: string;
    };
    rates: SWSExchangeRate[];
}

export interface SWSExchangeRate {
    from: string;
    to: string;
    rate: number;
}

export class SWSCurrencyProvider implements CurrencyProvider {

    constructor(private baseUrl: string, private tokenConfig: any) {}

    public async getRates(date: Date, baseCurrencies: string[], type = ExchangeRateType.BID): Promise<ExchangeRate> {
        const ts = getTimestamp(date);
        const rates = await this.requestRates(ts, type);
        const result: ExchangeRate = {
            provider: ExchangeRateProvider.SWS,
            type,
            ts,
            startTime: getStartTime(date),
            endTime: getEndTime(date),
            rates: {}
        };
        for (const data of rates.rates) {
            if (!result.rates[data.from]) {
                result.rates[data.from] = {};
            }
            result.rates[data.from][data.to] = data.rate;
        }
        return result;
    }

    private async requestRates(timestamp: string, type: ExchangeRateType): Promise<SWSExchangeRates> {
        const token = await generateToken(this.tokenConfig);
        return new Promise<SWSExchangeRates>((resolve, reject) => {
            return superagent.get(`${this.baseUrl}/v1/rates/${timestamp}`)
                .query({ token, type })
                .then(response => {
                    if (response.statusCode !== 200) {
                        log.error(response.body, "Currency rates call error");
                        return reject(
                            new Error(`SWS API internal error status=${response.statusCode}`)
                        );
                    }
                    return resolve(response.body);
                })
                .catch(error => reject(error));
        });
    }
}
