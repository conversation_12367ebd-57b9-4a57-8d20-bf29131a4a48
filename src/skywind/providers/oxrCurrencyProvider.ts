import { CurrencyProvider, ExchangeRate, ExchangeRateProvider, ExchangeRateType } from "../types";
import { getEndTime, getPrevDay, getStartTime, getTimestamp } from "../utils";
import * as superagent from "superagent";

const OXR_BASE_URL = "https://openexchangerates.org/api/historical";

export class OXRCurrencyProvider implements CurrencyProvider {

    constructor(private appId: string) {}

    public async getRates(date: Date, baseCurrencies: string[], type = ExchangeRateType.BID): Promise<ExchangeRate> {
        const ts = getTimestamp(getPrevDay(date, 2)); // use rates from two days ago
        const requests = baseCurrencies.map((baseCurrency) => this.requestRates(ts, baseCurrency));
        const responses = await Promise.all(requests);
        const result: ExchangeRate = {
            provider: ExchangeRateProvider.OXR,
            type,
            ts,
            startTime: getStartTime(date),
            endTime: getEndTime(date),
            rates: {}
        };
        for (const data of responses) {
            result.rates[data["base"]] = data["rates"];
        }
        return result;
    }

    private requestRates(timestamp: string, baseCurrency: string): Promise<any> {
        return new Promise<any>(async (resolve, reject) => {
            return superagent.get(`${OXR_BASE_URL}/${timestamp}.json`)
                .query({
                    "app_id": this.appId,
                    "base": baseCurrency
                })
                .then(response => {
                    if (response.statusCode !== 200) {
                        return reject(
                            new Error(`OXR internal error: ${response.body}, status=${response.statusCode}`)
                        );
                    }
                    return resolve(response.body);
                })
                .catch(error => reject(error));
        });
    }
}
