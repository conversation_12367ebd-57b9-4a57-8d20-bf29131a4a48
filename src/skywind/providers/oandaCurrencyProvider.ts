import { logging } from "@skywind-group/sw-utils";

import { CurrencyProvider, ExchangeRate, ExchangeRateProvider, ExchangeRateType } from "../types";
import { getEndTime, getPrevDay, getStartTime, getTimestamp } from "../utils";
import * as superagent from "superagent";

const OANDA_URL = "https://web-services.oanda.com/rates/api/v2/rates/candle.json";
const log = logging.logger("OANDA-currencies-data");

export class OANDACurrencyProvider implements CurrencyProvider {

    constructor(private apiKey: string) {}

    public async getRates(date: Date, baseCurrencies: string[], type = ExchangeRateType.BID): Promise<ExchangeRate> {
        const ts = getTimestamp(getPrevDay(date, 2)); // use rates from two days ago
        return new Promise<ExchangeRate>((resolve, reject) => {
            return superagent.get(`${OANDA_URL}`)
                .query({
                    "api_key": this.apiKey,
                    "base": baseCurrencies,
                    "date_time": ts,
                    "fields": "averages"
                })
                .then(response => {
                    if (response.statusCode !== 200) {
                        return reject(
                            new Error(`OANDA internal error: ${JSON.stringify(response.body)}, status=${response.statusCode}`)
                        );
                    }
                    const result = this.parseResponse(date, ts, response.body, type);
                    log.info({
                        requestData: {
                            dateTime: ts,
                            type
                        },
                        response: result
                    }, "Responce from OANDA");
                    return resolve(result);
                })
                .catch(error => reject(error));
        });
    }

    private parseResponse(date: Date, ts, data: any, type: ExchangeRateType): ExchangeRate {
        const result: ExchangeRate = {
            provider: ExchangeRateProvider.OANDA,
            type,
            ts,
            startTime: getStartTime(date),
            endTime: getEndTime(date),
            rates: {}
        };
        for (const rate of data["quotes"]) {
            const base = rate["base_currency"];
            const target = rate["quote_currency"];
            const directRate = type === ExchangeRateType.BID ? +rate["average_bid"] : +rate["average_ask"];
            const reverseRate = type === ExchangeRateType.BID ? +rate["average_ask"] : +rate["average_bid"];

            if (!result.rates[base]) {
                result.rates[base] = {};
            }
            result.rates[base][target] = directRate;

            if (!result.rates[target]) {
                result.rates[target] = {};
            }
            // calculate symmetric rate for backward conversion
            if (!result.rates[target][base]) {
                result.rates[target][base] = +((1 / reverseRate).toFixed(5));
            }
        }
        return result;
    }
}
