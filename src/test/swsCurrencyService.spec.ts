import config from "../skywind/config";
import { expect, use } from "chai";
import { CurrencyExchangeService } from "../skywind/currencyExchangeService";
import { flushDb, getRedisPool } from "./utils";
import { SWSCurrencyProvider, SWSExchangeRates } from "../skywind/providers/swsCurrencyProvider";
import { ExchangeRateType } from "../skywind/types";
import * as superagent from "superagent";
import superagentMock from "superagent-mock";

use(require("chai-as-promised"));

describe("SWS Currency Exchange", () => {
    let currencyExchange: CurrencyExchangeService;

    before(() => {
        currencyExchange = new CurrencyExchangeService();
    });

    beforeEach(async () => {
        await flushDb();
    });

    it("Get rates", async () => {
        const response: SWSExchangeRates = {
            date: "2019-02-07",
            source: {
                date: "2023-12-06",
                provider: "default"
            },
            rates: [
                {
                    from: "USD",
                    to: "EUR",
                    rate: 0.861603
                },
                {
                    from: "USD",
                    to: "CNY",
                    rate: 6.83090
                },
                {
                    from: "USD",
                    to: "VND",
                    rate: 23227.4
                }
            ]
        };
        const settings = [{
            fixtures: () => {
                return response;
            },
            get: (match: string[], data: SWSExchangeRates) => ({ statusCode: 200, body: data })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        await currencyExchange.init(getRedisPool(),
            new SWSCurrencyProvider(config.sws.baseUrl, config.sws.internalServerToken), config, ExchangeRateType.BID);

        expect(currencyExchange.exchange(100, "USD", "EUR")).to.be.equal(86.16);
        expect(currencyExchange.exchange(100, "USD", "CNY")).to.be.equal(683.08);
        expect(currencyExchange.exchange(100, "USD", "VND")).to.be.equal(2322740);
        mock.unset();
    });

    it("Fails to get rates", async () => {
        const settings = [{
            fixtures: () => {
                return {
                    "code": 1,
                    "message": "Internal server error"
                };
            },
            get: (match: string[], data: SWSExchangeRates) => ({ statusCode: 500, body: data })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        await expect(currencyExchange.init(getRedisPool(),
            new SWSCurrencyProvider(config.sws.baseUrl, config.sws.internalServerToken), config, ExchangeRateType.BID))
            .to.be.rejectedWith(Error);
        mock.unset();
    });
});
