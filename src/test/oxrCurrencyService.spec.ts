import config from "../skywind/config";
import { expect } from "chai";
import { CurrencyExchangeService } from "../skywind/currencyExchangeService";
import { OXRCurrencyProvider } from "../skywind/providers/oxrCurrencyProvider";
import { getRedisPool } from "./utils";
import { ExchangeRateType } from "../skywind/types";
import * as superagent from "superagent";
import superagentMock from "superagent-mock";

describe("OXR Currency Exchange", () => {
    let currencyExchange: CurrencyExchangeService;

    before(() => {
        currencyExchange = new CurrencyExchangeService();
    });

    it("Get rates", async () => {
        const settings = [{
            fixtures: () => {
                return {
                    timestamp: **********,
                    base: "USD",
                    rates: {
                        "EUR": 0.861603,
                        "CNY": 6.83090,
                        "VND": 23227.4
                    }
                };
            },
            get: (match: string[], data: any) => ({ statusCode: 200, body: data })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        await currencyExchange.init(getRedisPool(), new OXRCurrencyProvider("test"), config, ExchangeRateType.BID);

        expect(currencyExchange.exchange(100, "USD", "EUR")).to.be.equal(86.16);
        expect(currencyExchange.exchange(100, "USD", "CNY")).to.be.equal(683.08);
        expect(currencyExchange.exchange(100, "USD", "VND")).to.be.equal(2322740);
        mock.unset();
    });
});
