import RedisMock from "ioredis-mock";
import { Redis as RedisClient } from "ioredis";
import { RedisPool } from "../skywind/redisTypes";

const redisClient = new RedisMock();
const dbClient: RedisPool = {
    get: async (): Promise<RedisClient> => {
        return redisClient;
    },
    release: (client: RedisClient): Promise<void> => {
        return;
    },
    usingDb<T>(): Promise<T> {
        return;
    },
    usingDbWithReplicate<T>(): Promise<T> {
        return;
    },
    waitForSync(): Promise<void> {
        return;
    },
    shutdown(): Promise<void> {
        return;
    }
};

export function getRedisPool() {
    return dbClient;
}

export function getRedisClient() {
    return redisClient;
}

export async function flushDb(): Promise<void> {
    redisClient.flushdb();
}
