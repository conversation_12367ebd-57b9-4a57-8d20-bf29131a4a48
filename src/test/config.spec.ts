import { sanitizeSensitiveData } from "../skywind/config";
import { expect } from "chai";

const originalConfig = {
    oxr: { appKey: "awdwd123123awdkmalwkmdlakwd" },
    oanda: { apiKey: "alwkmdlkamwldkm1l2km3l1kmwdlak" },
    sws:
        {
            baseUrl: "http://currency-exchange-api:3030",
            internalServerToken:
                {
                    expiresIn: 300,
                    algorithm: "HS256",
                    issuer: "skywindgroup",
                    secret: "lakwmdlkamwdlk1ml2kmdlqkwmd"
                }
        },
    baseCurrencies: ["EUR", "USD", "CNY", "KRW", "MYR"],
    defaultBaseCurrency: "EUR",
    provider: "sws",
    updateSchedule: "0 2 * * *",
    failedUpdateTimeout: 60000,
    lockTtl: 30,
    lockRetryTimeout: 1000,
    dbRatesTtl: 172800,
    dbKeyPrefix: "currency:exchange:rate",
    someArray: ["test", "chebureck"]
};

describe("Config", () => {
    it("sanitize sensitive data", () => {
        const config = {
            oxr: { appKey: "awdwd123123awdkmalwkmdlakwd" },
            oanda: { apiKey: "alwkmdlkamwldkm1l2km3l1kmwdlak" },
            sws:
                {
                    baseUrl: "http://currency-exchange-api:3030",
                    internalServerToken:
                        {
                            expiresIn: 300,
                            algorithm: "HS256",
                            issuer: "skywindgroup",
                            secret: "lakwmdlkamwdlk1ml2kmdlqkwmd"
                        }
                },
            baseCurrencies: ["EUR", "USD", "CNY", "KRW", "MYR"],
            defaultBaseCurrency: "EUR",
            provider: "sws",
            updateSchedule: "0 2 * * *",
            failedUpdateTimeout: 60000,
            lockTtl: 30,
            lockRetryTimeout: 1000,
            dbRatesTtl: 172800,
            dbKeyPrefix: "currency:exchange:rate",
            someArray: ["test", "chebureck"]
        };

        const sanitizedConfig = sanitizeSensitiveData(config);

        expect(config).to.be.deep.equal(originalConfig);
        expect(sanitizedConfig.oxr.appKey).to.be.equal(`${config.oxr.appKey.substring(0, 5)}####`);
        expect(sanitizedConfig.oanda.apiKey).to.be.equal(`${config.oanda.apiKey.substring(0, 5)}####`);
        expect(sanitizedConfig.sws.internalServerToken.secret)
            .to.be.equal(`${config.sws.internalServerToken.secret.substring(0, 5)}####`);
    });
});
