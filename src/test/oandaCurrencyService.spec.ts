import config from "../skywind/config";
import { expect } from "chai";
import { CurrencyExchangeService } from "../skywind/currencyExchangeService";
import { OANDACurrencyProvider } from "../skywind/providers/oandaCurrencyProvider";
import { getRedisPool } from "./utils";
import { ExchangeRateType } from "../skywind/types";
import * as superagent from "superagent";
import superagentMock from "superagent-mock";

describe("OANDA Currency Exchange", () => {
    let currencyExchange: CurrencyExchangeService;

    before(() => {
        currencyExchange = new CurrencyExchangeService();
    });

    it("Get rates", async () => {
        const settings = [{
            fixtures: () => {
                return {
                    "quotes": [
                        {
                            "base_currency": "USD",
                            "quote_currency": "EUR",
                            "average_bid": "0.861603",
                            "average_ask": "0.861704"
                        },
                        {
                            "base_currency": "USD",
                            "quote_currency": "CNY",
                            "average_bid": "6.83090",
                            "average_ask": "6.83216"
                        },
                        {
                            "base_currency": "USD",
                            "quote_currency": "VND",
                            "average_bid": "23227.4",
                            "average_ask": "23363.2"
                        },
                        {
                            "base_currency": "EUR",
                            "quote_currency": "USD",
                            "average_bid": "1.16049",
                            "average_ask": "1.16063"
                        },
                        {
                            "base_currency": "EUR",
                            "quote_currency": "CNY",
                            "average_bid": "7.92719",
                            "average_ask": "7.92960"
                        },
                        {
                            "base_currency": "EUR",
                            "quote_currency": "MXN",
                            "average_bid": "7.92719",
                            "average_ask": "7.92960"
                        },
                        {
                            "base_currency": "EUR",
                            "quote_currency": "GBP",
                            "average_bid": "7.92719",
                            "average_ask": "7.92960"
                        },
                        {
                            "base_currency": "EUR",
                            "quote_currency": "CAD",
                            "average_bid": "7.92719",
                            "average_ask": "7.92960"
                        },
                    ]
                };
            },
            get: (match: string[], data: any) => ({ statusCode: 200, body: data })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        await currencyExchange.init(getRedisPool(), new OANDACurrencyProvider("test"), config, ExchangeRateType.BID);

        expect(currencyExchange.exchange(100, "USD", "EUR")).to.be.equal(86.16);
        expect(currencyExchange.exchange(100, "USD", "CNY")).to.be.equal(683.08);
        expect(currencyExchange.exchange(100, "USD", "VND")).to.be.equal(2322740);
        expect(currencyExchange.exchange(100, "EUR", "USD")).to.be.equal(116.04);
        expect(currencyExchange.exchange(100, "EUR", "CNY")).to.be.equal(792.71);
        expect(currencyExchange.exchange(1000, "EUX", "EUR")).to.be.equal(10);
        expect(currencyExchange.exchange(1000, "USX", "USD")).to.be.equal(10);
        expect(currencyExchange.exchange(1000, "MXX", "MXN")).to.be.equal(10);
        expect(currencyExchange.exchange(1000, "CDX", "CAD")).to.be.equal(10);
        expect(currencyExchange.exchange(1000, "GBX", "GBP")).to.be.equal(10);
        mock.unset();
    });
});
