import config from "../skywind/config";
import * as superagent from "superagent";
import superagentMock from "superagent-mock";
import { expect } from "chai";
import { GameLimitsCurrency } from "../skywind/types";
import {
    createGameLimitsCurrency,
    getGameLimitsCurrencies,
    getGameLimitsCurrency,
    updateGameLimitsCurrency
} from "../skywind/gameLimitsCurrencies";

describe("GameLimitsCurrencies", () => {
    it("getGameLimitsCurrencies", async () => {
        const currencies: GameLimitsCurrency[] = [
            {
                currency: "EUR",
                toEURMultiplier: 1.000000000000000,
                copyLimitsFrom: null,
                version: 1
            },
            {
                currency: "GBP",
                toEURMultiplier: 1.000000000000000,
                copyLimitsFrom: null,
                version: 1
            },
            {
                currency: "USD",
                toEURMultiplier: 1.000000000000000,
                copyLimitsFrom: "EUR",
                version: 1
            },
        ];
        const settings = [{
            pattern: `${config.sws.baseUrl}/v1/([^?]+)`,
            fixtures: (match: string[]) => {
                if (match[1] === "game-limits-currencies") {
                    return currencies;
                }
            },
            get: (match: string[], data: GameLimitsCurrency[]) => ({ body: data })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        const result = await getGameLimitsCurrencies();
        expect(result).deep.eq(currencies);
        mock.unset();
    });

    it("getGameLimitsCurrency", async () => {
        const currency = "USD";
        const version = 1;
        const responseCurrency: GameLimitsCurrency = {
            currency,
            toEURMultiplier: 1.000000000000000,
            copyLimitsFrom: "EUR",
            version
        };
        const settings = [{
            pattern: `${config.sws.baseUrl}/v1/([^?]+)`,
            fixtures: (match: string[]) => {
                if (match[1] === `game-limits-currencies/${currency}/${version}`) {
                    return responseCurrency;
                }
            },
            get: (match: string[], data: GameLimitsCurrency[]) => ({ body: data })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        const result = await getGameLimitsCurrency(currency, version);
        expect(result).deep.eq(responseCurrency);
        mock.unset();
    });

    it("updateGameLimitsCurrency", async () => {
        const currency = "USD";
        const version = 1;
        const responseCurrency: GameLimitsCurrency = {
            currency,
            toEURMultiplier: 1.000000000000000,
            copyLimitsFrom: "EUR",
            version
        };
        const settings = [{
            fixtures: () => {
                return responseCurrency;
            },
            patch: (match: string[], data: GameLimitsCurrency[]) => ({ body: data, statusCode: 200 })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        const result = await updateGameLimitsCurrency(currency, version, responseCurrency);
        expect(result).deep.eq(responseCurrency);
        mock.unset();
    });

    it("createGameLimitsCurrency", async () => {
        const currency = "USD";
        const version = 1;
        const responseCurrency: GameLimitsCurrency = {
            currency,
            toEURMultiplier: 1.000000000000000,
            copyLimitsFrom: "EUR",
            version
        };
        const settings = [{
            fixtures: () => {
                return responseCurrency;
            },
            post: (match: string[], data: GameLimitsCurrency[]) => ({ body: data, statusCode: 201 })
        }];
        const mock: superagentMock = superagentMock(superagent, settings);
        const result = await createGameLimitsCurrency(responseCurrency);
        expect(result).deep.eq(responseCurrency);
        mock.unset();
    });
});
