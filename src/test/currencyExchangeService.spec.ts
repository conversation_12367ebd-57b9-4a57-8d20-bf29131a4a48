import { expect, use } from "chai";
import { stub } from "sinon";
import {
    CurrencyNotFoundError,
    CurrencyRatesUnavailableError,
    ExchangeRate,
    ExchangeRateType,
    VirtualCurrencyRateError
} from "../skywind/types";
import { CurrencyExchangeService } from "../skywind/currencyExchangeService";
import config from "../skywind/config";
import { DefaultCurrencyProvider } from "../skywind/providers/defaultCurrencyProvider";
import { flushDb, getRedisClient, getRedisPool } from "./utils";
import { RedisPool } from "../skywind/redisTypes";

use(require("chai-as-promised"));

describe("Currency Exchange", () => {

    const DAY = 24 * 60 * 60 * 1000;

    const dbPool: RedisPool = getRedisPool();
    const dbKeyPrefix = `${config.dbKeyPrefix}:default`;
    let currencyExchange: CurrencyExchangeService;
    const provider = new DefaultCurrencyProvider();
    const type = ExchangeRateType.BID;
    let getRatesStub;
    let oldRates: ExchangeRate;
    let currentRates: ExchangeRate;
    let futureRates: ExchangeRate;

    before(async () => {
        currencyExchange = new CurrencyExchangeService();
        oldRates = await provider.getRates(new Date(Date.now() - DAY), config.baseCurrencies);
        currentRates = await provider.getRates(new Date(), config.baseCurrencies);
        futureRates = await provider.getRates(new Date(Date.now() + DAY), config.baseCurrencies);
        getRatesStub = stub(provider, "getRates");
    });

    afterEach(async () => {
        getRatesStub.reset();
        currencyExchange.clean();
        await flushDb();
    });

    it("init - gets data if Redis doesn't contain rates", async() => {
        getRatesStub.returns(currentRates);

        await currencyExchange.init(dbPool, provider, config, type);

        expect(getRatesStub.called).to.be.true;

        const dbKey = dbKeyPrefix + ":" + currentRates.ts + ":" + type;
        const data = await getRedisClient().get(dbKey);
        expect(JSON.parse(data).ts).to.be.equal(currentRates.ts);

        const currentDbKey = dbKeyPrefix + ":current" + ":" + type;
        const currentData = await getRedisClient().get(currentDbKey);
        expect(JSON.parse(currentData).ts).to.be.equal(currentRates.ts);
    });

    it("init - fail if nothing is available", async() => {
        const error = new Error("test");
        getRatesStub.throws(error);

        await expect(currencyExchange.init(dbPool, provider, config, type))
            .to.be.rejectedWith(CurrencyRatesUnavailableError);
    });

    it("init - use outdated rates if current not available", async() => {
        getRatesStub.throws(new Error("test"));

        // current time
        const dbKey = dbKeyPrefix + ":current" + ":" + type;
        await getRedisClient().set(dbKey, JSON.stringify(oldRates));

        await currencyExchange.init(dbPool, provider, config, type);

        const rates = currencyExchange.getExchangeRatesList();
        expect(rates.startTime).to.equal(oldRates.startTime);
        expect(rates.endTime).to.equal(oldRates.endTime);
    });

    it("init - must get data from Redis if it contains rates", async() => {
        getRatesStub.throws(new Error("test"));

        // current time
        const dbKey = dbKeyPrefix + ":" + currentRates.ts + ":" + type;
        await getRedisClient().set(dbKey, JSON.stringify(currentRates));

        await currencyExchange.init(dbPool, provider, config, type);

        // must be used rates from Redis
        expect(currencyExchange.exchange(100, "USD", "EUR")).to.be.equal(87.22);
        expect(currencyExchange.exchange(100, "EUR", "USD")).to.be.equal(114.64);
    });

    it("exchange - calculates rates correctly", async() => {
        getRatesStub.returns(currentRates);

        await currencyExchange.init(dbPool, provider, config, type);

        expect(currencyExchange.exchange(100, "USD", "EUR")).to.be.equal(87.22);
        expect(currencyExchange.exchange(100, "EUR", "USD")).to.be.equal(114.64);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "USD", "EUR"), "EUR", "USD"),
            "Conversion to EUR and back must return start amount").to.be.equal(99.99);
    });

    it("exchange - calculates rates correctly for artificial currencies", async() => {
        getRatesStub.returns(currentRates);

        await currencyExchange.init(dbPool, provider, config, type);

        // USD -> VNS
        expect(currencyExchange.exchange(100, "USD", "VNS")).to.be.equal(2325.389);
        expect(currencyExchange.exchange(100, "USD", "VND")).to.be.equal(2325389);
        expect(currencyExchange.exchange(100, "VNS", "USD")).to.be.equal(4.3);
        expect(currencyExchange.exchange(10000, "VND", "USD")).to.be.equal(0.43);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "USD", "VNS"), "VNS", "USD"),
            "Conversion to VNS and back must return start amount").to.be.equal(99.99);

        // VND -> VNS
        expect(currencyExchange.exchange(100, "VND", "VNS")).to.be.equal(0.1);
        expect(currencyExchange.exchange(100, "VNS", "VND")).to.be.equal(100000);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "VND", "VNS"), "VNS", "VND"),
            "Conversion to VNS and back must return start amount").to.be.equal(100);

        // USD -> IDS
        expect(currencyExchange.exchange(100, "USD", "IDS")).to.be.equal(1522.798);
        expect(currencyExchange.exchange(100, "USD", "IDR")).to.be.equal(1522798);
        expect(currencyExchange.exchange(100, "IDS", "USD")).to.be.equal(6.56);
        expect(currencyExchange.exchange(10000, "IDR", "USD")).to.be.equal(0.65);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "USD", "IDS"), "IDS", "USD"),
            "Conversion to IDS and back must return start amount").to.be.equal(100);

        // IDR -> IDS
        expect(currencyExchange.exchange(100, "IDR", "IDS")).to.be.equal(0.1);
        expect(currencyExchange.exchange(100, "IDS", "IDR")).to.be.equal(100000);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "IDR", "IDS"), "IDS", "IDR"),
            "Conversion to IDS and back must return start amount").to.be.equal(100);

        // VNS -> IDS
        expect(currencyExchange.exchange(100, "VNS", "IDS")).to.be.equal(65.485);
        expect(currencyExchange.exchange(100, "VND", "IDR")).to.be.equal(65.48);
        expect(currencyExchange.exchange(100, "IDS", "VNS")).to.be.equal(152.705);
        expect(currencyExchange.exchange(100, "IDR", "VND")).to.be.equal(152);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "VNS", "IDS"), "IDS", "VNS"),
            "Conversion to IDS and back must return start amount").to.be.equal(99.998);

        // VND -> VDO
        expect(currencyExchange.exchange(100, "VND", "VDO")).to.be.equal(0.1);
        expect(currencyExchange.exchange(100, "VDO", "VND")).to.be.equal(100000);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "VND", "VDO"), "VDO", "VND"),
            "Conversion to VDO and back must return start amount").to.be.equal(100);

        // RUP -> INR
        expect(currencyExchange.exchange(100, "INR", "RUP")).to.be.equal(0.1);
        expect(currencyExchange.exchange(100, "RUP", "INR")).to.be.equal(100000);
        expect(currencyExchange.exchange(currencyExchange.exchange(100, "INR", "RUP"), "RUP", "INR"),
            "Conversion to VDO and back must return start amount").to.be.equal(100);

        // UBT -> USD (BTC: 0.000044)
        expect(currencyExchange.exchange(1, "USD", "UBT")).to.be.equal(44);
        expect(currencyExchange.exchange(44, "UBT", "USD")).to.be.equal(1);

        // MBT -> USD (BTC: 0.000044)
        expect(currencyExchange.exchange(1, "USD", "MBT")).to.be.equal(0.044);
        expect(currencyExchange.exchange(0.044, "MBT", "USD")).to.be.equal(1);
    });

    it("Exchange with custom exchange rate", async () => {
        getRatesStub.returns(currentRates);
        await currencyExchange.init(dbPool, provider, config, type);

        const AMOUNT = 1234.567;

        // General currency
        const EUR_TO_USD_RATE = currencyExchange.getExchangeRate("USD", "EUR");
        expect(currencyExchange.exchange(AMOUNT, "USD", "EUR"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, EUR_TO_USD_RATE, "EUR"));

        const USD_TO_EURO_RATE = currencyExchange.getExchangeRate("EUR", "USD");
        expect(currencyExchange.exchange(AMOUNT, "USD", "EUR"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, 1 / USD_TO_EURO_RATE, "EUR"));

        // Artificial currencies
        const VNS_TO_EURO = currencyExchange.getExchangeRate("EUR", "VNS");
        expect(currencyExchange.exchange(AMOUNT, "VNS", "EUR"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, 1 / VNS_TO_EURO, "EUR"));

        expect(currencyExchange.exchange(AMOUNT, "EUR", "VNS"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, VNS_TO_EURO, "VNS"));

        const EURO_TO_VNS = currencyExchange.getExchangeRate("VNS", "EUR");
        expect(currencyExchange.exchange(AMOUNT, "VNS", "EUR"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, EURO_TO_VNS, "EUR"));

        const EURO_TO_VND = currencyExchange.getExchangeRate("VND", "EUR");
        expect(currencyExchange.exchange(AMOUNT, "VND", "EUR"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, EURO_TO_VND, "EUR"));

        const VND_TO_EURO = currencyExchange.getExchangeRate("EUR", "VND");
        expect(currencyExchange.exchange(AMOUNT, "EUR", "VND"))
            .deep.equal(currencyExchange.exchangeWithRate(AMOUNT, VND_TO_EURO, "VND"));

        // Synthetic test
        expect(currencyExchange.exchangeWithRate(1234.567, 1 / 2, "USD")).deep.equal(617.28);
        expect(currencyExchange.exchangeWithRate(1234.567, 1 / 2, "VND")).deep.equal(617);
        expect(currencyExchange.exchangeWithRate(1234.567, 1 / 2, "EUR")).deep.equal(617.28);
        expect(currencyExchange.exchangeWithRate(33333, 1 / 3, "EUR")).deep.equal(11111);
        expect(currencyExchange.exchangeWithRate(33333, 3, "EUR")).deep.equal(99999);
    });

    it("Exchange rates list", async () => {
        getRatesStub.returns(currentRates);
        await currencyExchange.init(dbPool, provider, config, type);

        const result = currencyExchange.getExchangeRatesList();

        expect(result.rates["USD"]).to.exist;

        expect(result.rates["USD"]["IDR"]).deep.equal(15227.98);
        expect(result.rates["USD"]["IDS"]).deep.equal(15.227979999999999);

        expect(result.rates["USD"]["VND"]).deep.eq(23253.897672);
        expect(result.rates["USD"]["VNS"]).deep.eq(23.253897672);
        expect(result.rates["USD"]["EUX"]).deep.eq(87.224);
        expect(result.rates["USD"]["USX"]).deep.eq(100);
        expect(result.rates["USD"]["MXX"]).deep.eq(1940.7215);
        expect(result.rates["USD"]["CDX"]).deep.eq(130.9928);
        expect(result.rates["USD"]["GBX"]).deep.eq(77.1289);
    });

    it("Exchange with bad currency code", async() => {
        expect(() => currencyExchange.getExchangeRate("FAKE", "EUR")).to.throw(CurrencyNotFoundError);
    });

    it("Exchange to the same currency", async () => {
        getRatesStub.returns(currentRates);

        await currencyExchange.init(dbPool, provider, config, type);

        expect(currencyExchange.exchange(10.03, "USD", "USD")).to.be.equal(10.03);
        expect(currencyExchange.exchange(10.03, "EUR", "EUR")).to.be.equal(10.03);
        expect(currencyExchange.exchange(1000, "EUR", "EUR")).to.be.equal(1000);
        expect(currencyExchange.exchangeWithRate(10.03, 1, "USD")).to.be.equal(10.03);
    });

    it("Exchange with custom rates", () => {
        expect(currencyExchange.exchange(10, "USD", "EUR", { "USD": { "EUR": 0.9 } })).to.be.equal(9);
        expect(currencyExchange.exchange(9, "EUR", "USD", { "USD": { "EUR": 0.9 } })).to.be.equal(10);
    });

    it("Exchange virtual currency", () => {
        expect(currencyExchange.exchange(100, "BNS", "USD", { "BNS": { "USD": 0.1 } })).to.be.equal(10);
        expect(currencyExchange.exchange(10, "USD", "BNS", { "BNS": { "USD": 0.1 } })).to.be.equal(100);
    });

    it("Exchange with conversion currency", async() => {
        getRatesStub.returns(currentRates);

        const newConfig = { ...config };
        newConfig.defaultBaseCurrency = "SOS";
        await currencyExchange.init(dbPool, provider, newConfig, type);

        expect(currencyExchange.exchange(100, "TOP", "PYG")).to.be.equal(259406);
        expect(currencyExchange.exchange(100, "PYG", "TOP")).to.be.equal(0.03);
    });

    it("Fails to exchange virtual currency without custom exchange rates", () => {
        expect(() => currencyExchange.exchange(10, "BNS", "USD")).to.throw(VirtualCurrencyRateError);
        expect(() => currencyExchange.exchange(10, "BNS", "USD", { "USD": { "EUR": 0.9 } }))
            .to.throw(VirtualCurrencyRateError);
    });

    it("Fails to exchange with undefined rate", async () => {
        getRatesStub.returns({
            provider: currentRates.provider,
            ts: currentRates.ts,
            startTime: currentRates.startTime,
            endTime: currentRates.endTime,
            rates: {}
        } as ExchangeRate);

        await currencyExchange.init(dbPool, provider, config, type);

        expect(() => currencyExchange.exchange(10, "EUR", "USD")).to.throw(CurrencyRatesUnavailableError);
    });

    it("Uses ask/bid currency rates if both defined", async () => {
        getRatesStub.returns({
            provider: currentRates.provider,
            type,
            ts: currentRates.ts,
            startTime: currentRates.startTime,
            endTime: currentRates.endTime,
            rates: {
                "USD": {
                    "EUR": 0.861603
                },
                "EUR": {
                    "USD": 1.16049
                }
            }
        } as ExchangeRate);

        await currencyExchange.init(dbPool, provider, config, type);

        expect(currencyExchange.exchange(100, "USD", "EUR")).to.be.equal(86.16);
        expect(currencyExchange.exchange(100, "EUR", "USD")).to.be.equal(116.04);
    });

    it("Skips duplicate currency rate updates", async () => {
        getRatesStub.onCall(0).returns(currentRates);
        getRatesStub.onCall(1).returns(currentRates);
        getRatesStub.throws(new Error("test"));

        await currencyExchange.init(dbPool, provider, config, type);
        await new CurrencyExchangeService().init(dbPool, provider, config, type);

        expect(getRatesStub.callCount).to.eq(2);
    });

    it("Schedules currency rate updates", async () => {

        const cfg = Object.assign({}, config, {
            dbRatesTtl: 1,
            updateSchedule: "* * * * * *"
        });

        getRatesStub.returns(currentRates);

        await currencyExchange.init(dbPool, provider, cfg, type);

        getRatesStub.returns(futureRates);

        await new Promise((resolve) => {
            setTimeout(resolve, 4000);
        });

        expect(getRatesStub.callCount).to.be.gte(3);
    });

    it("Schedules currency rate updates - retries failed updates", async () => {
        const cfg = {
            ...config,
            failedUpdateTimeout: 1,
            dbRatesTtl: 1,
            updateSchedule: "* * * * * *"
        };

        getRatesStub.onCall(0).returns(currentRates);
        getRatesStub.onCall(1).returns(futureRates);
        getRatesStub.onCall(2).throws(new Error("test"));
        getRatesStub.onCall(3).throws(new Error("test"));
        getRatesStub.onCall(4).returns(futureRates);

        await currencyExchange.init(dbPool, provider, cfg, type);

        await new Promise((resolve) => {
            setTimeout(resolve, 5000);
        });

        expect(getRatesStub.callCount).to.be.gte(5);
    });
});
