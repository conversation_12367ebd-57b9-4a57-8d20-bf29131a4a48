{"compilerOptions": {"target": "es2021", "lib": ["es2021"], "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "outDir": "lib", "allowSyntheticDefaultImports": true, "sourceMap": true, "isolatedModules": false, "experimentalDecorators": true, "emitDecoratorMetadata": true, "declaration": true, "noImplicitAny": false, "removeComments": true, "noLib": false, "preserveConstEnums": true, "inlineSources": false, "skipLibCheck": true, "ignoreDeprecations": "5.0", "useDefineForClassFields": false, "resolveJsonModule": true}, "include": ["src/**/*.ts", "src/**/*.json"], "exclude": ["node_modules"]}